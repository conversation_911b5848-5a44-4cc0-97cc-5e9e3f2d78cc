package routes

import (
	"adc-multi-languages/config"
	"adc-multi-languages/handlers"
	"adc-multi-languages/middleware"
	"adc-multi-languages/utils"

	"github.com/gin-gonic/gin"
)

// SetupRoutes configures all routes for the application
func SetupRoutes(cfg *config.Config) *gin.Engine {
	// Set Gin mode based on environment
	if cfg.IsProduction() {
		gin.SetMode(gin.ReleaseMode)
	}

	// Create Gin router
	router := gin.New()

	// Add middleware
	router.Use(gin.Recovery())
	router.Use(utils.LoggerMiddleware())

	// CORS configuration
	corsConfig := middleware.CORSConfig{
		AllowedOrigins:   cfg.AllowedOrigins,
		AllowedMethods:   []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"Origin", "Content-Type", "Accept", "Authorization", "X-Requested-With", "X-API-Key"},
		ExposedHeaders:   []string{"Content-Length", "Content-Type"},
		AllowCredentials: true,
		MaxAge:           86400,
	}
	router.Use(middleware.CORS(corsConfig))

	// Auth configuration
	authConfig := middleware.AuthConfig{
		JWTSecret:                cfg.JWTSecret,
		JWTExpirationHours:       cfg.JWTExpirationHours,
		JWTRefreshExpirationDays: cfg.JWTRefreshExpirationDays,
	}

	// Initialize handlers
	apiHandler := handlers.NewAPIHandler()
	authHandler := handlers.NewAuthHandler(authConfig)
	userHandler := handlers.NewUserHandler()
	organizationHandler := handlers.NewOrganizationHandler()
	projectHandler := handlers.NewProjectHandler()
	translationHandler := handlers.NewTranslationHandler()
	localeHandler := handlers.NewLocaleHandler()
	apiKeyHandler := handlers.NewAPIKeyHandler()
	permissionGroupHandler := handlers.NewPermissionGroupHandler()
	aiCreditHandler := handlers.NewAICreditHandler()
	adminHandler := handlers.NewAdminHandler()
	subscriptionPlanHandler := handlers.NewSubscriptionPlanHandler()
	webhookHandler := handlers.NewWebhookHandler()
	stripeCheckoutHandler := handlers.NewStripeCheckoutHandler()
	testHandler := handlers.NewTestHandler()
	aiTranslationHandler := handlers.NewAITranslationHandler()
	fileUploadHandler := handlers.NewFileUploadHandler()
	metricsHandler := handlers.NewMetricsHandler()
	backgroundJobHandler := handlers.NewBackgroundJobHandler()
	creditLimitHandler := handlers.NewCreditLimitHandler()
	orgSubscriptionHandler := handlers.NewOrganizationSubscriptionHandler()
	aiCreditsPurchaseHandler := handlers.NewAICreditsPurchaseHandler()
	apiKeyUsageHandler := handlers.NewAPIKeyUsageHandler()
	auditLogsHandler := handlers.NewAuditLogsHandler()
	projectResourcesHandler := handlers.NewProjectResourcesHandler()
	slugLookupHandler := handlers.NewSlugLookupHandler()
	creditUsageHandler := handlers.NewCreditUsageHandler()

	// Root routes
	router.GET("/", apiHandler.Index)
	router.GET("/health", apiHandler.Health)

	// API routes
	api := router.Group("/api")
	{
		// Public API routes
		api.GET("/hello", apiHandler.Hello)
		api.GET("/hello/:name", apiHandler.HelloWithName)
		api.POST("/echo", apiHandler.Echo)
		api.GET("/error-example", apiHandler.ErrorExample)

		// Auth routes (public) with rate limiting
		auth := api.Group("/auth")
		auth.Use(middleware.AuthRateLimit()) // Apply auth-specific rate limiting
		{
			auth.POST("/signup", authHandler.SignUp)
			auth.POST("/signin", authHandler.SignIn)
			auth.POST("/refresh-token", authHandler.RefreshToken)
			auth.POST("/request-password-reset", authHandler.RequestPasswordReset)
			auth.POST("/confirm-password-reset", authHandler.ConfirmPasswordReset)
			auth.POST("/request-email-verification", authHandler.RequestEmailVerification)
			auth.POST("/verify-email", authHandler.VerifyEmail)
			auth.POST("/google-auth", authHandler.GoogleAuth)
		}

		// Public API routes (no authentication required)
		// Locales (public for language selection)
		api.GET("/locales", localeHandler.ListLocales)

		// Subscription Plans (public for pricing display)
		api.GET("/subscription-plans", subscriptionPlanHandler.ListSubscriptionPlans)
		api.GET("/subscription-plans/:id", subscriptionPlanHandler.GetSubscriptionPlan)

		// AI Credits pricing (public for pricing display)
		api.GET("/ai-credits/pricing", aiCreditsPurchaseHandler.GetAICreditsPricing)
		api.GET("/ai-credits/pricing/calculate", aiCreditsPurchaseHandler.GetPricingCalculation)

		// Audit logs utility routes (public for form dropdowns)
		api.GET("/audit-logs/actions", auditLogsHandler.GetAuditLogActions)
		api.GET("/audit-logs/resource-types", auditLogsHandler.GetAuditLogResourceTypes)

		// Protected routes (require JWT authentication)
		protected := api.Group("")
		protected.Use(middleware.JWTAuth(authConfig))
		protected.Use(middleware.GeneralRateLimit()) // Apply general rate limiting
		{
			// Users routes
			users := protected.Group("/users")
			{
				users.GET("/me", userHandler.GetProfile)
				users.PUT("/me", userHandler.UpdateProfile)
				users.GET("/:id", userHandler.GetUser)
			}

			// Locale routes (protected - for admin operations)
			locales := protected.Group("/locales")
			{
				locales.POST("", localeHandler.CreateLocale)
				locales.GET("/:id", localeHandler.GetLocale)
				locales.PUT("/:id", localeHandler.UpdateLocale)
				locales.DELETE("/:id", localeHandler.DeleteLocale)
			}

			// Organizations routes
			organizations := protected.Group("/organizations")
			{
				organizations.GET("", organizationHandler.ListOrganizations)
				organizations.POST("", organizationHandler.CreateOrganization)
				organizations.GET("/:orgId", organizationHandler.GetOrganization)
				organizations.PUT("/:orgId", organizationHandler.UpdateOrganization)
				organizations.DELETE("/:orgId", organizationHandler.DeleteOrganization)

				// Organization members routes
				organizations.GET("/:orgId/members", organizationHandler.ListOrganizationMembers)

				// Organization payment methods routes
				organizations.GET("/:orgId/payment-methods", organizationHandler.ListOrganizationPaymentMethods)
			}

			// Projects routes
			projects := protected.Group("/projects")
			{
				projects.GET("", projectHandler.ListProjects)
				projects.POST("", projectHandler.CreateProject)
				projects.GET("/:id", projectHandler.GetProject)
				projects.PUT("/:id", projectHandler.UpdateProject)
				projects.DELETE("/:id", projectHandler.DeleteProject)

				// Project resources routes
				projects.GET("/:id/locales", projectResourcesHandler.GetProjectLocales)
				projects.POST("/:id/locales", projectResourcesHandler.AddProjectLocale)
				projects.GET("/:id/resources", projectResourcesHandler.GetProjectResources)
			}

			// Project slug routes
			projectSlug := protected.Group("/projects/slug")
			{
				projectSlug.GET("/:slug", slugLookupHandler.GetProjectBySlug)
				projectSlug.GET("/:slug/available", slugLookupHandler.CheckProjectSlugAvailability)
			}

			// Organization slug routes
			orgSlug := protected.Group("/organizations/slug")
			{
				orgSlug.GET("/:slug", slugLookupHandler.GetOrganizationBySlug)
				orgSlug.GET("/:slug/available", slugLookupHandler.CheckOrganizationSlugAvailability)
			}

			// Translations routes
			translations := protected.Group("/translations")
			{
				translations.GET("/keys", translationHandler.ListTranslationKeys)
				translations.POST("/keys", translationHandler.CreateTranslationKey)
				translations.GET("/keys/:id", translationHandler.GetTranslationKey)
				translations.POST("", translationHandler.CreateTranslation)
				translations.GET("", translationHandler.ListTranslations)
				translations.GET("/:id", translationHandler.GetTranslation)
				translations.PUT("/:id", translationHandler.UpdateTranslation)
				translations.GET("/:id/history", translationHandler.GetTranslationHistory)
			}

			// Admin routes
			admin := protected.Group("/admin")
			{
				admin.GET("/dashboard", adminHandler.Dashboard)
				admin.GET("/users", adminHandler.ListUsers)
				admin.GET("/users/:id", adminHandler.GetUser)
				admin.PUT("/users/:id/status", adminHandler.UpdateUserStatus)
				admin.POST("/subscription-plans", subscriptionPlanHandler.CreateSubscriptionPlan)
				admin.PUT("/subscription-plans/:id", subscriptionPlanHandler.UpdateSubscriptionPlan)
			}

			// API Keys routes (within organization context)
			organizations.GET("/:orgId/api-keys", apiKeyHandler.ListAPIKeys)
			organizations.POST("/:orgId/api-keys", apiKeyHandler.CreateAPIKey)
			organizations.GET("/:orgId/api-keys/:id", apiKeyHandler.GetAPIKey)
			organizations.PUT("/:orgId/api-keys/:id", apiKeyHandler.UpdateAPIKey)
			organizations.DELETE("/:orgId/api-keys/:id", apiKeyHandler.DeleteAPIKey)

			// Permission Groups routes (within organization context)
			organizations.GET("/:orgId/permission-groups", permissionGroupHandler.ListPermissionGroups)
			organizations.POST("/:orgId/permission-groups", permissionGroupHandler.CreatePermissionGroup)
			organizations.GET("/:orgId/permission-groups/:id", permissionGroupHandler.GetPermissionGroup)

			// AI Credits routes (within organization context)
			organizations.GET("/:orgId/ai-credits", aiCreditHandler.GetAICredits)
			organizations.GET("/:orgId/ai-credits/history", aiCreditHandler.GetAICreditsHistory)
			organizations.GET("/:orgId/ai-credits/usage", aiCreditHandler.GetAICreditsUsage)

			// Credit limit routes
			organizations.GET("/:orgId/credit-limit", creditLimitHandler.GetCreditLimit)
			organizations.PUT("/:orgId/credit-limit", creditLimitHandler.UpdateCreditLimit)

			// Organization subscription routes
			organizations.GET("/:orgId/subscription", orgSubscriptionHandler.GetOrganizationSubscription)
			organizations.POST("/:orgId/subscription", orgSubscriptionHandler.CreateOrganizationSubscription)
			organizations.DELETE("/:orgId/subscription", orgSubscriptionHandler.CancelOrganizationSubscription)
			organizations.PUT("/:orgId/subscription/verify", orgSubscriptionHandler.VerifyOrganizationSubscription)

			// AI credits purchase routes
			organizations.POST("/:orgId/ai-credits/purchase", aiCreditsPurchaseHandler.PurchaseAICredits)

			// Stripe routes (within organization context)
			stripe := protected.Group("/stripe")
			{
				stripe.POST("/create-checkout-session", stripeCheckoutHandler.CreateCheckoutSession)
				stripe.GET("/checkout-session/:sessionId", stripeCheckoutHandler.GetCheckoutSession)
			}

			// Admin Stripe routes
			admin.GET("/stripe/checkout-sessions", stripeCheckoutHandler.ListCheckoutSessions)

			// API key usage analytics routes
			organizations.GET("/:orgId/api-keys/:id/usage", apiKeyUsageHandler.GetAPIKeyUsage)
			organizations.GET("/:orgId/api-keys/:id/usage/by-endpoint", apiKeyUsageHandler.GetAPIKeyUsageByEndpoint)
			organizations.GET("/:orgId/api-keys/:id/usage/by-day", apiKeyUsageHandler.GetAPIKeyUsageByDay)

			// Audit logs routes
			organizations.GET("/:orgId/audit-logs", auditLogsHandler.GetOrganizationAuditLogs)
			organizations.GET("/:orgId/api-keys/:id/audit-logs", auditLogsHandler.GetAPIKeyAuditLogs)

			// Credit usage analytics routes
			organizations.GET("/:orgId/credit-usage", creditUsageHandler.GetCreditUsageHistory)
			organizations.GET("/:orgId/credit-analytics", creditUsageHandler.GetCreditUsageAnalytics)

			// AI Translation routes
			ai := protected.Group("/ai")
			{
				ai.POST("/translate", aiTranslationHandler.TranslateText)
				ai.POST("/detect-language", aiTranslationHandler.DetectLanguage)
				ai.GET("/supported-languages", aiTranslationHandler.GetSupportedLanguages)
				ai.GET("/translation-history", aiTranslationHandler.GetTranslationHistory)
			}

			// File Upload routes
			files := protected.Group("/files")
			{
				files.POST("/upload", fileUploadHandler.UploadFile)
				files.POST("/upload-multiple", fileUploadHandler.UploadMultipleFiles)
				files.GET("/config", fileUploadHandler.GetUploadConfig)
				files.GET("/stats", fileUploadHandler.GetUploadStats)
				files.GET("/:id/info", fileUploadHandler.GetFileInfo)
				files.GET("/:id/download", fileUploadHandler.DownloadFile)
				files.DELETE("/:id", fileUploadHandler.DeleteFile)
			}

			// Metrics routes
			metrics := protected.Group("/metrics")
			{
				metrics.GET("", metricsHandler.GetAllMetrics)
				metrics.GET("/system", metricsHandler.GetSystemMetrics)
				metrics.GET("/endpoints", metricsHandler.GetEndpointMetrics)
				metrics.GET("/api-keys", metricsHandler.GetAPIKeyMetrics)
				metrics.GET("/translations", metricsHandler.GetTranslationMetrics)
				metrics.GET("/active-users", metricsHandler.GetActiveUsers)
				metrics.GET("/health", metricsHandler.GetHealthMetrics)
				metrics.POST("/reset", metricsHandler.ResetMetrics)
			}

			// Background Jobs routes
			jobs := protected.Group("/jobs")
			{
				jobs.POST("", backgroundJobHandler.CreateJob)
				jobs.GET("", backgroundJobHandler.GetUserJobs)
				jobs.GET("/types", backgroundJobHandler.GetJobTypes)
				jobs.GET("/stats", backgroundJobHandler.GetJobStats)
				jobs.GET("/:id", backgroundJobHandler.GetJob)
				jobs.DELETE("/:id", backgroundJobHandler.CancelJob)
				jobs.POST("/:id/retry", backgroundJobHandler.RetryJob)
			}

			// Test routes (development only)
			test := protected.Group("/test")
			{
				test.POST("/email", testHandler.TestEmail)
				test.POST("/verification-email", testHandler.TestVerificationEmail)
				test.GET("/database", testHandler.TestDatabaseConnection)
				test.POST("/sample-data", testHandler.TestCreateSampleData)
			}
		}

		// Webhook routes (public, but with special authentication)
		webhooks := api.Group("/webhooks")
		{
			webhooks.POST("/stripe", webhookHandler.StripeWebhook)
		}

		// File serving routes (public)
		router.GET("/uploads/*filepath", fileUploadHandler.ServeFile)

		// Prometheus metrics endpoint (public)
		router.GET("/metrics", metricsHandler.GetPrometheusMetrics)

	}

	return router
}
