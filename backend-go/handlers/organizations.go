package handlers

import (
	"fmt"
	"regexp"
	"strings"
	"time"

	"adc-multi-languages/database"
	"adc-multi-languages/middleware"
	"adc-multi-languages/models"
	"adc-multi-languages/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// OrganizationHandler handles organization-related routes
type OrganizationHandler struct{}

// NewOrganizationHandler creates a new organization handler
func NewOrganizationHandler() *OrganizationHandler {
	return &OrganizationHandler{}
}

// Request structures
type CreateOrganizationRequest struct {
	Name        string  `json:"name" binding:"required,min=1,max=100"`
	Slug        string  `json:"slug" binding:"required,min=1,max=50"`
	Description *string `json:"description"`
	Website     *string `json:"website"`
	LogoURL     *string `json:"logo_url"`
}

type UpdateOrganizationRequest struct {
	Name        *string `json:"name"`
	Slug        *string `json:"slug"`
	Description *string `json:"description"`
	Website     *string `json:"website"`
	LogoURL     *string `json:"logo_url"`
}

// Response structures
type OrganizationResponse struct {
	ID                        string     `json:"id"`
	Name                      string     `json:"name"`
	Slug                      string     `json:"slug"`
	Description               *string    `json:"description"`
	Website                   *string    `json:"website"`
	LogoURL                   *string    `json:"logo_url"`
	OwnerID                   string     `json:"owner_id"`
	SubscriptionTier          string     `json:"subscription_tier"`
	SubscriptionStatus        *string    `json:"subscription_status"`
	SubscriptionTierID        *string    `json:"subscription_tier_id"`
	SubscriptionAutoRenew     *bool      `json:"subscription_auto_renew"`
	BillingPeriodStart        *time.Time `json:"billing_period_start"`
	BillingPeriodEnd          *time.Time `json:"billing_period_end"`
	StripeCustomerID          *string    `json:"stripe_customer_id"`
	StripeSubscriptionID      *string    `json:"stripe_subscription_id"`
	AICreditsMonthlyAllowance *int       `json:"ai_credits_monthly_allowance"`
	AICreditsRemaining        *int       `json:"ai_credits_remaining"`
	AICreditsResetDate        *time.Time `json:"ai_credits_reset_date"`
	CreatedAt                 time.Time  `json:"created_at"`
	UpdatedAt                 time.Time  `json:"updated_at"`
	Role                      *string    `json:"role,omitempty"` // User's role in this organization
}

// ListOrganizations handles GET /api/organizations
func (h *OrganizationHandler) ListOrganizations(c *gin.Context) {
	// Get authenticated user from context
	user, exists := middleware.GetAuthUser(c)
	if !exists {
		utils.Unauthorized(c, "Authentication required")
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Get pagination parameters
	pagination := utils.GetPaginationParams(c)

	// Find organizations where user is a member
	var organizations []models.Organization
	var total int64

	// Count total organizations for user
	if err := db.Table("organizations").
		Joins("JOIN organization_memberships ON organizations.id = organization_memberships.organization_id").
		Where("organization_memberships.user_id = ? AND organization_memberships.is_active = ?", user.ID, true).
		Count(&total).Error; err != nil {
		utils.LogError(c, "Failed to count user organizations", err, nil)
		utils.InternalServerError(c, "Failed to retrieve organizations")
		return
	}

	// Get organizations with pagination
	if err := db.Preload("Owner").
		Joins("JOIN organization_memberships ON organizations.id = organization_memberships.organization_id").
		Where("organization_memberships.user_id = ? AND organization_memberships.is_active = ?", user.ID, true).
		Limit(int(pagination.PerPage)).
		Offset(int(pagination.Offset)).
		Find(&organizations).Error; err != nil {
		utils.LogError(c, "Failed to list user organizations", err, nil)
		utils.InternalServerError(c, "Failed to retrieve organizations")
		return
	}

	// Get user roles for each organization
	var members []models.OrganizationMembership
	orgIDs := make([]uuid.UUID, len(organizations))
	for i, org := range organizations {
		orgIDs[i] = org.ID
	}

	if len(orgIDs) > 0 {
		if err := db.Where("organization_id IN ? AND user_id = ?", orgIDs, user.ID).
			Find(&members).Error; err != nil {
			utils.LogError(c, "Failed to get user roles", err, nil)
			utils.InternalServerError(c, "Failed to retrieve user roles")
			return
		}
	}

	// Create role map
	roleMap := make(map[uuid.UUID]string)
	for _, member := range members {
		roleMap[member.OrganizationID] = member.Role
	}

	// Convert to response format
	response := make([]OrganizationResponse, len(organizations))
	for i, org := range organizations {
		role := roleMap[org.ID]
		response[i] = OrganizationResponse{
			ID:                        org.ID.String(),
			Name:                      org.Name,
			Slug:                      org.Slug,
			Description:               org.Description,
			Website:                   org.Website,
			LogoURL:                   org.LogoURL,
			OwnerID:                   org.OwnerID.String(),
			SubscriptionTier:          org.SubscriptionTier,
			SubscriptionStatus:        org.SubscriptionStatus,
			SubscriptionTierID:        uuidPtrToStringPtr(org.SubscriptionTierID),
			SubscriptionAutoRenew:     org.SubscriptionAutoRenew,
			BillingPeriodStart:        org.BillingPeriodStart,
			BillingPeriodEnd:          org.BillingPeriodEnd,
			StripeCustomerID:          org.StripeCustomerID,
			StripeSubscriptionID:      org.StripeSubscriptionID,
			AICreditsMonthlyAllowance: org.AICreditsMonthlyAllowance,
			AICreditsRemaining:        org.AICreditsRemaining,
			AICreditsResetDate:        org.AICreditsResetDate,
			CreatedAt:                 org.CreatedAt,
			UpdatedAt:                 org.UpdatedAt,
			Role:                      &role,
		}
	}

	utils.PaginatedResponse(c, response, pagination, uint64(total))
}

// CreateOrganization handles POST /api/organizations
func (h *OrganizationHandler) CreateOrganization(c *gin.Context) {
	// Get authenticated user from context
	user, exists := middleware.GetAuthUser(c)
	if !exists {
		utils.Unauthorized(c, "Authentication required")
		return
	}

	var req CreateOrganizationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	// Validate slug format
	if !isValidSlug(req.Slug) {
		utils.BadRequest(c, "Slug must contain only lowercase letters, numbers, and hyphens")
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Check if organization with same slug already exists
	var existingOrg models.Organization
	if err := db.Where("slug = ?", req.Slug).First(&existingOrg).Error; err == nil {
		utils.Conflict(c, fmt.Sprintf("Organization with slug '%s' already exists", req.Slug))
		return
	}

	// Parse user ID
	userID, err := uuid.Parse(user.ID)
	if err != nil {
		utils.BadRequest(c, "Invalid user ID")
		return
	}

	// Create organization
	organization := models.Organization{
		Name:                      req.Name,
		Slug:                      req.Slug,
		Description:               req.Description,
		Website:                   req.Website,
		LogoURL:                   req.LogoURL,
		OwnerID:                   userID,
		SubscriptionTier:          "free",
		AICreditsMonthlyAllowance: intPtr(1000),
		AICreditsRemaining:        intPtr(1000),
		AICreditsResetDate:        timePtr(time.Now().AddDate(0, 1, 0)), // Next month
	}

	// Start transaction
	tx := db.Begin()

	// Create organization
	if err := tx.Create(&organization).Error; err != nil {
		tx.Rollback()
		if strings.Contains(err.Error(), "duplicate key") || strings.Contains(err.Error(), "unique constraint") {
			utils.Conflict(c, "Organization with this name or slug already exists")
			return
		}
		utils.LogError(c, "Failed to create organization", err, nil)
		utils.InternalServerError(c, "Failed to create organization")
		return
	}

	// Add creator as admin member
	member := models.OrganizationMembership{
		OrganizationID: organization.ID,
		UserID:         userID,
		Role:           "admin",
		JoinedAt:       timePtr(time.Now()),
		IsActive:       true,
	}

	if err := tx.Create(&member).Error; err != nil {
		tx.Rollback()
		utils.LogError(c, "Failed to add organization member", err, nil)
		utils.InternalServerError(c, "Failed to create organization")
		return
	}

	// Find the free subscription plan
	var freePlan models.SubscriptionPlan
	if err := tx.Where("name = ? AND is_active = ?", "Free", true).First(&freePlan).Error; err != nil {
		tx.Rollback()
		utils.LogError(c, "Failed to find free subscription plan", err, nil)
		utils.InternalServerError(c, "Failed to create organization")
		return
	}

	// Create free subscription for the organization
	now := time.Now()
	subscription := models.OrganizationSubscription{
		OrganizationID:     organization.ID,
		SubscriptionPlanID: freePlan.ID,
		Status:             "active",
		BillingPeriod:      "monthly",
		CurrentPeriodStart: now,
		CurrentPeriodEnd:   now.AddDate(0, 1, 0), // Next month
		CancelAtPeriodEnd:  false,
	}

	if err := tx.Create(&subscription).Error; err != nil {
		tx.Rollback()
		utils.LogError(c, "Failed to create organization subscription", err, nil)
		utils.InternalServerError(c, "Failed to create organization")
		return
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		utils.LogError(c, "Failed to commit organization creation", err, nil)
		utils.InternalServerError(c, "Failed to create organization")
		return
	}

	// Reload organization with owner
	if err := db.Preload("Owner").Where("id = ?", organization.ID).First(&organization).Error; err != nil {
		utils.LogError(c, "Failed to reload created organization", err, nil)
		utils.InternalServerError(c, "Failed to retrieve created organization")
		return
	}

	// Convert to response format
	response := OrganizationResponse{
		ID:                        organization.ID.String(),
		Name:                      organization.Name,
		Slug:                      organization.Slug,
		Description:               organization.Description,
		Website:                   organization.Website,
		LogoURL:                   organization.LogoURL,
		OwnerID:                   organization.OwnerID.String(),
		SubscriptionTier:          organization.SubscriptionTier,
		SubscriptionStatus:        organization.SubscriptionStatus,
		SubscriptionTierID:        uuidPtrToStringPtr(organization.SubscriptionTierID),
		SubscriptionAutoRenew:     organization.SubscriptionAutoRenew,
		BillingPeriodStart:        organization.BillingPeriodStart,
		BillingPeriodEnd:          organization.BillingPeriodEnd,
		StripeCustomerID:          organization.StripeCustomerID,
		StripeSubscriptionID:      organization.StripeSubscriptionID,
		AICreditsMonthlyAllowance: organization.AICreditsMonthlyAllowance,
		AICreditsRemaining:        organization.AICreditsRemaining,
		AICreditsResetDate:        organization.AICreditsResetDate,
		CreatedAt:                 organization.CreatedAt,
		UpdatedAt:                 organization.UpdatedAt,
		Role:                      stringPtr("admin"),
	}

	utils.SuccessWithMessage(c, response, "Organization created successfully")
}

// Helper functions

// isValidSlug validates slug format
func isValidSlug(slug string) bool {
	// Slug should contain only lowercase letters, numbers, and hyphens
	// Should not start or end with hyphen
	match, _ := regexp.MatchString(`^[a-z0-9]+(-[a-z0-9]+)*$`, slug)
	return match && len(slug) >= 1 && len(slug) <= 50
}

// uuidPtrToStringPtr converts *uuid.UUID to *string
func uuidPtrToStringPtr(u *uuid.UUID) *string {
	if u == nil {
		return nil
	}
	s := u.String()
	return &s
}

// intPtr returns a pointer to an int
func intPtr(i int) *int {
	return &i
}

// timePtr returns a pointer to a time.Time
func timePtr(t time.Time) *time.Time {
	return &t
}

// stringPtr returns a pointer to a string
func stringPtr(s string) *string {
	return &s
}

// GetOrganization handles GET /api/organizations/:id
func (h *OrganizationHandler) GetOrganization(c *gin.Context) {
	// Get authenticated user from context
	user, exists := middleware.GetAuthUser(c)
	if !exists {
		utils.Unauthorized(c, "Authentication required")
		return
	}

	orgIDStr := c.Param("id")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Check if user is a member of this organization
	var member models.OrganizationMembership
	if err := db.Where("organization_id = ? AND user_id = ? AND is_active = ?", orgID, user.ID, true).
		First(&member).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "You don't have access to this organization")
			return
		}
		utils.LogError(c, "Failed to check organization membership", err, nil)
		utils.InternalServerError(c, "Failed to verify organization access")
		return
	}

	// Get organization
	var organization models.Organization
	if err := db.Preload("Owner").Where("id = ?", orgID).First(&organization).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "Organization not found")
			return
		}
		utils.LogError(c, "Failed to get organization", err, nil)
		utils.InternalServerError(c, "Failed to retrieve organization")
		return
	}

	// Convert to response format
	response := OrganizationResponse{
		ID:                        organization.ID.String(),
		Name:                      organization.Name,
		Slug:                      organization.Slug,
		Description:               organization.Description,
		Website:                   organization.Website,
		LogoURL:                   organization.LogoURL,
		OwnerID:                   organization.OwnerID.String(),
		SubscriptionTier:          organization.SubscriptionTier,
		SubscriptionStatus:        organization.SubscriptionStatus,
		SubscriptionTierID:        uuidPtrToStringPtr(organization.SubscriptionTierID),
		SubscriptionAutoRenew:     organization.SubscriptionAutoRenew,
		BillingPeriodStart:        organization.BillingPeriodStart,
		BillingPeriodEnd:          organization.BillingPeriodEnd,
		StripeCustomerID:          organization.StripeCustomerID,
		StripeSubscriptionID:      organization.StripeSubscriptionID,
		AICreditsMonthlyAllowance: organization.AICreditsMonthlyAllowance,
		AICreditsRemaining:        organization.AICreditsRemaining,
		AICreditsResetDate:        organization.AICreditsResetDate,
		CreatedAt:                 organization.CreatedAt,
		UpdatedAt:                 organization.UpdatedAt,
		Role:                      &member.Role,
	}

	utils.Success(c, response)
}

// ListOrganizationMembers handles GET /api/organizations/:orgId/members
func (h *OrganizationHandler) ListOrganizationMembers(c *gin.Context) {
	orgID := c.Param("orgId")

	// Validate organization ID
	orgUUID, err := uuid.Parse(orgID)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	// Get current user
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not found in context")
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		utils.InternalServerError(c, "Invalid user ID in context")
		return
	}

	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		utils.InternalServerError(c, "Invalid user ID format")
		return
	}

	db := database.GetDB()

	// Check if user is a member of this organization
	var userMember models.OrganizationMembership
	if err := db.Where("organization_id = ? AND user_id = ? AND is_active = ?", orgUUID, userUUID, true).
		First(&userMember).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "You don't have access to this organization")
			return
		}
		utils.LogError(c, "Failed to check organization membership", err, nil)
		utils.InternalServerError(c, "Failed to verify organization access")
		return
	}

	// Get pagination parameters
	pagination := utils.GetPaginationParams(c)

	// Get organization members
	var members []models.OrganizationMembership
	var total int64

	// Count total members
	if err := db.Where("organization_id = ? AND is_active = ?", orgUUID, true).
		Count(&total).Error; err != nil {
		utils.LogError(c, "Failed to count organization members", err, nil)
		utils.InternalServerError(c, "Failed to retrieve members count")
		return
	}

	// Get members with user details
	if err := db.Preload("User").
		Where("organization_id = ? AND is_active = ?", orgUUID, true).
		Limit(int(pagination.PerPage)).
		Offset(int(pagination.Offset)).
		Find(&members).Error; err != nil {
		utils.LogError(c, "Failed to list organization members", err, nil)
		utils.InternalServerError(c, "Failed to retrieve organization members")
		return
	}

	// Use the proper pagination response
	utils.SuccessWithPagination(c, members, pagination.Page, pagination.PerPage, uint64(total))
}

// ListOrganizationPaymentMethods handles GET /api/organizations/:orgId/payment-methods
func (h *OrganizationHandler) ListOrganizationPaymentMethods(c *gin.Context) {
	orgID := c.Param("orgId")

	// Validate organization ID
	orgUUID, err := uuid.Parse(orgID)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	// Get current user
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not found in context")
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		utils.InternalServerError(c, "Invalid user ID in context")
		return
	}

	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		utils.InternalServerError(c, "Invalid user ID format")
		return
	}

	db := database.GetDB()

	// Check if user is a member of this organization
	var userMember models.OrganizationMembership
	if err := db.Where("organization_id = ? AND user_id = ? AND is_active = ?", orgUUID, userUUID, true).
		First(&userMember).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "You don't have access to this organization")
			return
		}
		utils.LogError(c, "Failed to check organization membership", err, nil)
		utils.InternalServerError(c, "Failed to verify organization access")
		return
	}

	// For now, return empty array since payment methods aren't implemented yet
	// In the future, this would fetch Stripe payment methods for the organization
	paymentMethods := []interface{}{}

	utils.Success(c, paymentMethods)
}

// UpdateOrganization handles PUT /api/organizations/:id
func (h *OrganizationHandler) UpdateOrganization(c *gin.Context) {
	// Get authenticated user from context
	user, exists := middleware.GetAuthUser(c)
	if !exists {
		utils.Unauthorized(c, "Authentication required")
		return
	}

	orgIDStr := c.Param("id")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	var req UpdateOrganizationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Check if user is admin of this organization
	var member models.OrganizationMembership
	if err := db.Where("organization_id = ? AND user_id = ? AND role = ? AND is_active = ?",
		orgID, user.ID, "admin", true).First(&member).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "You don't have admin access to this organization")
			return
		}
		utils.LogError(c, "Failed to check organization admin access", err, nil)
		utils.InternalServerError(c, "Failed to verify organization access")
		return
	}

	// Get organization
	var organization models.Organization
	if err := db.Where("id = ?", orgID).First(&organization).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "Organization not found")
			return
		}
		utils.LogError(c, "Failed to get organization", err, nil)
		utils.InternalServerError(c, "Failed to retrieve organization")
		return
	}

	// Prepare update data
	updates := map[string]interface{}{
		"updated_at": time.Now(),
	}

	// Validate and update fields
	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Slug != nil {
		if !isValidSlug(*req.Slug) {
			utils.BadRequest(c, "Slug must contain only lowercase letters, numbers, and hyphens")
			return
		}
		// Check if slug is already taken by another organization
		var existingOrg models.Organization
		if err := db.Where("slug = ? AND id != ?", *req.Slug, orgID).First(&existingOrg).Error; err == nil {
			utils.Conflict(c, fmt.Sprintf("Organization with slug '%s' already exists", *req.Slug))
			return
		}
		updates["slug"] = *req.Slug
	}
	if req.Description != nil {
		updates["description"] = req.Description
	}
	if req.Website != nil {
		updates["website"] = req.Website
	}
	if req.LogoURL != nil {
		updates["logo_url"] = req.LogoURL
	}

	// Update organization
	if err := db.Model(&organization).Updates(updates).Error; err != nil {
		if strings.Contains(err.Error(), "duplicate key") || strings.Contains(err.Error(), "unique constraint") {
			utils.Conflict(c, "Organization with this name or slug already exists")
			return
		}
		utils.LogError(c, "Failed to update organization", err, nil)
		utils.InternalServerError(c, "Failed to update organization")
		return
	}

	// Reload organization with owner
	if err := db.Preload("Owner").Where("id = ?", orgID).First(&organization).Error; err != nil {
		utils.LogError(c, "Failed to reload updated organization", err, nil)
		utils.InternalServerError(c, "Failed to retrieve updated organization")
		return
	}

	// Convert to response format
	response := OrganizationResponse{
		ID:                        organization.ID.String(),
		Name:                      organization.Name,
		Slug:                      organization.Slug,
		Description:               organization.Description,
		Website:                   organization.Website,
		LogoURL:                   organization.LogoURL,
		OwnerID:                   organization.OwnerID.String(),
		SubscriptionTier:          organization.SubscriptionTier,
		SubscriptionStatus:        organization.SubscriptionStatus,
		SubscriptionTierID:        uuidPtrToStringPtr(organization.SubscriptionTierID),
		SubscriptionAutoRenew:     organization.SubscriptionAutoRenew,
		BillingPeriodStart:        organization.BillingPeriodStart,
		BillingPeriodEnd:          organization.BillingPeriodEnd,
		StripeCustomerID:          organization.StripeCustomerID,
		StripeSubscriptionID:      organization.StripeSubscriptionID,
		AICreditsMonthlyAllowance: organization.AICreditsMonthlyAllowance,
		AICreditsRemaining:        organization.AICreditsRemaining,
		AICreditsResetDate:        organization.AICreditsResetDate,
		CreatedAt:                 organization.CreatedAt,
		UpdatedAt:                 organization.UpdatedAt,
		Role:                      &member.Role,
	}

	utils.SuccessWithMessage(c, response, "Organization updated successfully")
}

// DeleteOrganization handles DELETE /api/organizations/:id
func (h *OrganizationHandler) DeleteOrganization(c *gin.Context) {
	// Get authenticated user from context
	user, exists := middleware.GetAuthUser(c)
	if !exists {
		utils.Unauthorized(c, "Authentication required")
		return
	}

	orgIDStr := c.Param("id")
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Check if user is the owner of this organization
	var organization models.Organization
	if err := db.Where("id = ? AND owner_id = ?", orgID, user.ID).First(&organization).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "You can only delete organizations you own")
			return
		}
		utils.LogError(c, "Failed to get organization", err, nil)
		utils.InternalServerError(c, "Failed to retrieve organization")
		return
	}

	// Soft delete organization (using GORM's soft delete)
	if err := db.Delete(&organization).Error; err != nil {
		utils.LogError(c, "Failed to delete organization", err, nil)
		utils.InternalServerError(c, "Failed to delete organization")
		return
	}

	utils.SuccessWithMessage(c, map[string]string{
		"message": "Organization deleted successfully",
	}, "Organization deleted successfully")
}
